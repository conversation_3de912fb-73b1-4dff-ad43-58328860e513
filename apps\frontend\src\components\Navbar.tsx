"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import PrimaryBtn from "./ui/PrimaryBtn";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import { ChevronDown, ChevronUp, Search } from "lucide-react";
import { motion } from "framer-motion";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faBook,
  faBookOpenReader,
  faScroll,
  IconDefinition,
  faPrayingHands,
  faDove,
  faArrowRight,
} from "@fortawesome/free-solid-svg-icons";
import { useHoverDropdown } from "@/hooks/useHoverDropdown";
export default function Navbar() {
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const searchInputRef = useRef<HTMLInputElement>(null);

  // Focus on search input immediately when search is clicked
  useEffect(() => {
    if (isSearchOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isSearchOpen]);

  // Close search and clear input
  const handleCloseSearch = () => {
    setIsSearchOpen(false);
    if (searchInputRef.current) {
      searchInputRef.current.value = "";
    }
  };

  // === Why use a custom hook for the dropdown? ===
  //
  // The dropdown is not a child of the button, so we
  // cannot check the button's hover state.
  //
  // I attempted to solve this problem by creating a custom hook that uses
  // a timeout to delay the closing of the dropdown.
  //
  // - Nathan
  const faithFormationDropdown = useHoverDropdown();
  const exploreDropdown = useHoverDropdown();

  return (
    <nav className="bg-white px-4 sm:px-6 md:px-8 lg:px-12 relative">
      <div className="flex gap-4 justify-between items-center p-4">
        <div className="flex-1 flex items-center gap-6">
          <div>
            <Image
              src="/icons/fish-logo.svg"
              alt="logo"
              width={70}
              height={32}
            />
          </div>

          {/* Show normal navbar when search is closed */}
          {!isSearchOpen && (
            <div className="flex-1 flex items-center justify-between">
              <div className="flex items-center gap-6">
                <div
                  className="relative"
                  onMouseEnter={faithFormationDropdown.onMouseEnter}
                  onMouseLeave={faithFormationDropdown.onMouseLeave}
                >
                  <Button variant="bordered" className="p-0">
                    <motion.div
                      className="flex items-center gap-1"
                      animate={{
                        opacity: faithFormationDropdown.isOpen ? 0.6 : 1,
                      }}
                      transition={{ duration: 0.2 }}
                    >
                      <span>Faith Formation</span>
                      {faithFormationDropdown.isOpen ? (
                        <ChevronUp size={18} />
                      ) : (
                        <ChevronDown size={18} />
                      )}
                    </motion.div>
                  </Button>
                </div>
                <div
                  className="relative"
                  onMouseEnter={exploreDropdown.onMouseEnter}
                  onMouseLeave={exploreDropdown.onMouseLeave}
                >
                  <Button variant="bordered" className="p-0">
                    <motion.div
                      className="flex items-center gap-1"
                      animate={{
                        opacity: exploreDropdown.isOpen ? 0.6 : 1,
                      }}
                      transition={{ duration: 0.2 }}
                    >
                      <span>Explore</span>
                      {exploreDropdown.isOpen ? (
                        <ChevronUp size={18} />
                      ) : (
                        <ChevronDown size={18} />
                      )}
                    </motion.div>
                  </Button>
                </div>
                <Link
                  href="#"
                  className="hover:text-gray-500 transition-colors"
                >
                  About
                </Link>
                <Link
                  href="#"
                  className="hover:text-gray-500 transition-colors"
                >
                  Contact
                </Link>

                <div className="w-px h-6 bg-gray-300 select-none pointer-events-none ml-2" />

                <Button isIconOnly onPress={() => setIsSearchOpen(true)}>
                  <Search size={16} />
                </Button>
              </div>
            </div>
          )}

          {/* Show search bar when search is open */}
          {isSearchOpen && (
            <div className="flex-1 flex items-center justify-center relative">
              <div className="max-w-2xl w-full p-1.5 px-4 border-1 border-gray-300 rounded-full flex gap-3 items-center">
                <Search size={16} />
                <input
                  ref={searchInputRef}
                  type="search"
                  placeholder="Search `website name`..."
                  className='flex-1 border-0 outline-0 bg-transparent placeholder:text-sm placeholder:font-["Poppins"] [&::-webkit-search-cancel-button]:appearance-none [&::-webkit-search-decoration]:appearance-none'
                />
              </div>
            </div>
          )}
        </div>

        {/* Show normal buttons when search is closed */}
        {!isSearchOpen && (
          <>
            <PrimaryBtn text="Sign up" link="#" outline />
            <PrimaryBtn text="Listen now" link="#" />
          </>
        )}

        {/* Show cancel button when search is open */}
        {isSearchOpen && (
          <Button variant="light" onPress={handleCloseSearch} className="ml-4">
            <span className="text-sm font-medium font-['Poppins'] leading-none">
              Cancel
            </span>
          </Button>
        )}
      </div>

      {/* Show quick links when search is open */}
      {isSearchOpen && (
        <div className="absolute left-0 right-0 bg-white shadow-lg z-50 border-t border-gray-200">
          <SearchQuickLinks />
        </div>
      )}

      {/* Only show dropdowns when search is closed */}
      {!isSearchOpen && faithFormationDropdown.isOpen && (
        <div
          className="absolute left-0 right-0 bg-white shadow-lg z-50"
          onMouseEnter={faithFormationDropdown.onMouseEnter}
          onMouseLeave={faithFormationDropdown.onMouseLeave}
        >
          <FaithFormationDropdown />
        </div>
      )}

      {!isSearchOpen && exploreDropdown.isOpen && (
        <div
          className="absolute left-0 right-0 bg-white shadow-lg z-50"
          onMouseEnter={exploreDropdown.onMouseEnter}
          onMouseLeave={exploreDropdown.onMouseLeave}
        >
          <ExploreDropdown />
        </div>
      )}
    </nav>
  );
}

function SearchQuickLinks() {
  return (
    <div className="px-4 sm:px-6 md:px-8 lg:px-12 py-4">
      <div className="max-w-2xl mx-auto">
        <div className='text-gray-600 text-sm font-medium font-["Poppins"] tracking-wide mb-3'>
          Quick Links
        </div>
        <div className="space-y-2">
          <SearchQuickLink title="Connect with a website administrator" />
          <SearchQuickLink title="Devotionals" />
          <SearchQuickLink title="`website name` Blog" />
          <SearchQuickLink title="Discipleship Sign-up" />
          <SearchQuickLink title="`Radio Station Name`" />
        </div>
      </div>
    </div>
  );
}

function SearchQuickLink({ title }: { title: string }) {
  return (
    <Link
      href="#"
      className="block py-2 text-gray-900 text-sm font-medium font-['Poppins'] hover:text-gray-500 transition-colors"
    >
      {/* font awesome right arrow */}
      <FontAwesomeIcon icon={faArrowRight} className="text-gray-400 mr-2" />
      {title}
    </Link>
  );
}

function DropdownWrapper({ children }: { children: React.ReactNode }) {
  return (
    <>
      <div className="pb-2 flex justify-center">
        <div className="max-w-4xl w-full justify-center gap-8 hidden md:flex">

          {children}
        </div>
      </div>
      <div className="py-2" />
    </>
  );
}

function MobileDropdownWrapper({ children }: { children: React.ReactNode }) {
  return <div className="px-4 py-4 md:hidden">{children}</div>;
}

function FaithFormationDropdown() {
  return (
    <>
      {/* Desktop version */}
      <DropdownWrapper>
        {/* foundations column */}
        <div className="flex flex-col justify-start items-start">
          <div className="px-6 pt-6 flex justify-center items-center gap-2.5">
            <div className="text-center text-gray-600 text-xs font-medium font-['Poppins'] leading-none">
              FOUNDATION BLOCKS
            </div>
          </div>
          <div className="px-6 flex flex-col justify-start items-start">
            <DropdownItem
              title="Christian Foundations"
              description="New to faith? Explore the building blocks of what it means to be a Believer."
            />
            <div className="w-96 border-t border-secondary-100" />
            <DropdownItem
              title="Know God"
              description="Delve into truths to unlock who God is and why He matters for you."
            />
            <div className="w-96 border-t border-secondary-100" />
            <DropdownItem
              title="Disciple"
              description="Explore different ways of following Jesus with community, from mentorship to small groups."
            />
          </div>
        </div>
        {/* deep dive column */}
        <div className="flex flex-col justify-start items-start border-l border-secondary-100 pl-8">
          <div className="px-6 pt-6 flex justify-center items-center gap-2.5">
            <div className="text-center text-gray-600 text-xs font-medium font-['Poppins'] leading-none">
              DEEPER DIVE
            </div>
          </div>
          <div className="px-6 flex flex-col justify-start items-start gap-2">
            <DropdownItem
              title="Navigating Life's Challenges"
              description="Find biblical guidance and encouragement for facing struggles, doubts, and everyday decisions."
            />
            <div className="w-96 border-t border-secondary-100" />
            <DropdownItem
              title="Repentance"
              description="Understand what it means to turn away from sin and walk in the freedom and grace of Jesus."
            />
            <div className="w-96 border-t border-secondary-100" />
            <DropdownItem
              title="Salvation"
              description="Explore how to receive new life in Christ and what it means to be saved by faith."
            />
          </div>
        </div>
        {/* the good news column */}
        <div className="flex flex-col justify-start items-start border-l border-secondary-100 pl-8">
          <div className="px-6 pt-6 flex justify-center items-center gap-2.5">
            <div className="text-center text-gray-600 text-xs font-medium font-['Poppins'] leading-none">
              THE GOOD NEWS
            </div>
          </div>
          <div className="px-6 flex flex-col justify-start items-start gap-2">
            <DropdownItem
              title="The Good News"
              description="Read the story Of God's love, redemption, and plan to restore humanity."
            />

            <div className="w-96 border-t border-secondary-100" />
            <DropdownItemSmall title="Old Testament" icon={faScroll} />
            <div className="w-96 border-t border-secondary-100" />
            <DropdownItemSmall title="New Testament" icon={faDove} />
          </div>
        </div>
      </DropdownWrapper>

      {/* Mobile version */}
      <MobileDropdownWrapper>
        <div className="space-y-4">
          <div>
            <h3 className="text-gray-600 text-xs font-medium font-['Poppins'] uppercase tracking-wide mb-3">
              Foundations
            </h3>
            <div className="space-y-2">
              <MobileDropdownItem title="Christian Foundations" />
              <MobileDropdownItem title="Know God" />
              <MobileDropdownItem title="The Good News" />
            </div>
          </div>
          <div>
            <h3 className="text-gray-600 text-xs font-medium font-['Poppins'] uppercase tracking-wide mb-3">
              Growth
            </h3>
            <div className="space-y-2">
              <MobileDropdownItem title="Navigating Life's Challenges" />
              <MobileDropdownItem title="Repentance" />
              <MobileDropdownItem title="Salvation" />
            </div>
          </div>
        </div>
      </MobileDropdownWrapper>
    </>
  );
}

function ExploreDropdown() {
  return (
    <>
      {/* Desktop version */}
      <DropdownWrapper>
        <div className="flex flex-col justify-start items-start">
          <div className="px-6 pt-6 flex justify-center items-center gap-2.5">
            <div className="text-center text-gray-600 text-xs font-medium font-['Poppins'] leading-none">
              EQUIPPED TO FOLLOW
            </div>
          </div>
          <div className="px-6 flex flex-col justify-start items-start">
            <DropdownItem
              title="Resources"
              description="Tools and guides to support your spiritual walk, from study materials to recommended books."
            />
            <div className="w-96 border-t border-secondary-100" />
            <DropdownItem
              title="What's Next"
              description="Take the next step in your faith journey, whether you're new to Jesus or ready to grow deeper."
            />
          </div>
        </div>
        <div className="flex flex-col justify-start items-start border-l border-secondary-100 pl-8">
          <div className="px-6 pt-6 flex justify-center items-center gap-2.5">
            <div className="text-center text-gray-600 text-xs font-medium font-['Poppins'] leading-none">
              DAILY LIVING
            </div>
          </div>
          <div className="px-6 flex flex-col justify-start items-start gap-2">
            <DropdownItem
              title="Blog"
              description="Explore thoughtful reflections, biblical insights, and real-life stories about following Jesus."
            />
            <div className="w-96 border-t border-secondary-100" />

            <DropdownItemSmall
              title="Stories of Transformation"
              icon={faBookOpenReader}
            />
            <div className="w-96 border-t border-secondary-100" />
            <DropdownItemSmall title="Prayers" icon={faPrayingHands} />
            <div className="w-96 border-t border-secondary-100" />

            <DropdownItemSmall title="Verses" icon={faScroll} />
            <div className="w-96 border-t border-secondary-100" />
            <DropdownItemSmall title="Devotionals" icon={faBook} />
          </div>
        </div>

        <div className="flex flex-col justify-start items-start border-l border-secondary-100 pl-8">
          <div className="px-6 pt-6 flex justify-center items-center gap-2.5">
            <div className="text-center text-gray-600 text-xs font-medium font-['Poppins'] leading-none">
              GOT QUESTIONS?
            </div>
          </div>
          <div className="px-6 flex flex-col justify-start items-start gap-2">
            <DropdownItem
              title="Faith FAQs"
              description="Honest and simple answers to common questions about the Christian faith, God, the Bible, and salvation."
            />
            <div className="w-96 border-t border-secondary-100" />
            <DropdownItem
              title="General FAQs"
              description="Check out answers to frequently asked questions about navigating the site or understanding how things work here."
            />
          </div>
        </div>
      </DropdownWrapper>

      {/* Mobile version */}
      <MobileDropdownWrapper>
        <div className="space-y-4">
          <div>
            <h3 className="text-gray-600 text-xs font-medium font-['Poppins'] uppercase tracking-wide mb-3">
              Equipped to Follow
            </h3>
            <div className="space-y-2">
              <MobileDropdownItem title="Resources" />
              <MobileDropdownItem title="What's Next" />
            </div>
          </div>
          <div>
            <h3 className="text-gray-600 text-xs font-medium font-['Poppins'] uppercase tracking-wide mb-3">
              Daily Living
            </h3>
            <div className="space-y-2">
              <MobileDropdownItem title="Blog" />
              <MobileDropdownItemWithIcon
                title="Stories of Transformation"
                icon={faBookOpenReader}
              />
              <MobileDropdownItemWithIcon
                title="Prayers"
                icon={faPrayingHands}
              />
              <MobileDropdownItemWithIcon title="Verses" icon={faScroll} />
              <MobileDropdownItemWithIcon title="Devotionals" icon={faBook} />
            </div>
          </div>
          <div>
            <h3 className="text-gray-600 text-xs font-medium font-['Poppins'] uppercase tracking-wide mb-3">
              Got Questions?
            </h3>
            <div className="space-y-2">
              <MobileDropdownItem title="Faith FAQs" />
              <MobileDropdownItem title="General FAQs" />
            </div>
          </div>
        </div>
      </MobileDropdownWrapper>
    </>
  );
}

function DropdownItem({
  title,
  description,
}: {
  title: string;
  description: string;
}) {
  return (
    <div className="w-96 py-6 flex flex-col justify-start items-start group cursor-pointer">
      <Link
        href="#"
        className="self-stretch justify-center text-gray-900 text-xl font-semibold font-['Poppins'] leading-7 group-hover:text-gray-500 transition-colors"
      >
        {title}
      </Link>
      <div className="w-80 justify-center text-gray-600 text-sm font-normal font-['Poppins'] leading-tight mt-1 group-hover:text-gray-400 transition-colors">
        {description}
      </div>
    </div>
  );
}

function DropdownItemSmall({
  title,
  icon,
}: {
  title: string;
  icon: IconDefinition;
}) {
  return (
    <div className="w-96 flex flex-col justify-center items-start group cursor-pointer">
      <Link
        href="#"
        className="self-stretch justify-center py-1 text-gray-900 text-sm font-semibold font-['Poppins'] group-hover:text-gray-500 transition-colors"
      >
        <div className="flex items-center gap-2">
          <FontAwesomeIcon
            icon={icon}
            style={{ width: "16px", height: "16px", verticalAlign: "middle" }}
          />
          <span>{title}</span>
        </div>
      </Link>
    </div>
  );
}

function MobileDropdownItem({ title }: { title: string }) {
  return (
    <Link
      href="#"
      className="block py-2 text-gray-900 text-base font-medium font-['Poppins'] hover:text-gray-500 transition-colors"
    >
      {title}
    </Link>
  );
}

function MobileDropdownItemWithIcon({
  title,
  icon,
}: {
  title: string;
  icon: IconDefinition;
}) {
  return (
    <Link
      href="#"
      className="block py-2 text-gray-900 text-base font-medium font-['Poppins'] hover:text-gray-500 transition-colors"
    >
      <div className="flex items-center gap-2">
        <FontAwesomeIcon
          icon={icon}
          style={{ width: "16px", height: "16px", verticalAlign: "middle" }}
        />
        <span>{title}</span>
      </div>
    </Link>
  );
}