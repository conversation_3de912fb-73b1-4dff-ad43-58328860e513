{"name": "gro-turbo-repo", "private": true, "scripts": {"build:frontend": "turbo run build --filter=frontend", "dev:frontend": "turbo run dev --filter=frontend", "build:api": "turbo run build --filter=api", "dev:api": "turbo run dev --filter=api", "build:dashboard": "turbo run build --filter=cms-dashboard", "dev:dashboard": "turbo run dev --filter=cms-dashboard", "dev": "turbo run dev", "start": "turbo run start", "lint": "turbo run lint --filter=frontend", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "clean": "sh ./scripts/npm-clean-install.sh"}, "devDependencies": {"@types/eslint": "^9.0.0", "@types/validator": "^13.15.2", "autoprefixer": "^10.4.21", "eslint": "^9.0.0", "eslint-config-next": "^15.3.3", "postcss": "^8.5.6", "prettier": "^3.5.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwindcss": "^4.1.11", "turbo": "^2.4.2", "typescript": "5.7.3"}, "overrides": {"react": "^18.3.1", "react-dom": "^18.3.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7"}, "engines": {"node": ">=18"}, "packageManager": "npm@10.1.0", "workspaces": ["apps/*", "packages/*"], "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@heroui/button": "^2.2.21", "@heroui/react": "^2.7.10", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "next": "^15.4.2"}}