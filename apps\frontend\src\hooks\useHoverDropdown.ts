import { useRef, useState, useCallback } from 'react';

export function useHoverDropdown(delay = 100) {
  const [isOpen, setIsOpen] = useState(false);
  const hoverTimeout = useRef<NodeJS.Timeout | null>(null);

  const handleMouseEnter = useCallback(() => {
    if (hoverTimeout.current) clearTimeout(hoverTimeout.current);
    setIsOpen(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    hoverTimeout.current = setTimeout(() => {
      setIsOpen(false);
    }, delay);
  }, [delay]);

  return {
    isOpen,
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave,
  };
}