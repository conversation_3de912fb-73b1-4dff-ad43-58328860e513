"use client";

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import PrimaryBtn from './ui/PrimaryBtn';
import SecondaryBtn from './ui/SecondaryBtn';

interface Story {
  id: number;
  name: string;
  location: string;
  image: string;
  avatar: string;
  title: string;
  number: string;
  number_description: string;
  resources: string;
  change: string;
  link: string;
}

export default function StoriesOfTransformation() {
  const [activeStory, setActiveStory] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isImageHovered, setIsImageHovered] = useState(false);
  const autoSlideInterval = 10000; // 10 seconds per story
  const progressRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(Date.now());
  
  const stories: Story[] = [
    {
      id: 0,
      name: "<PERSON><PERSON>",
      location: "United Arab Emirates",
      image: "/images/stories/aisha-story.jpg",
      avatar: "/images/stories/aisha-story.jpg",
      title: "See how <PERSON><PERSON> found peace beyond success through an unexpected encounter with <PERSON>",
      number: "2.3+ million",
      number_description: "individuals exploring faith through our outreach programs",
      resources: "Bible Study plans, Podcast",
      change: "Renewed identity, deeper peace, spiritual purpose",
      link: "about:blank"
    },
    {
      id: 1,
      name: "Amina",
      location: "Indonesia",
      image: "/images/stories/amina-story.jpg",
      avatar: "/images/stories/amina-story.jpg",
      title: "See how Amina found hope and healing through our online mentorship program",
      number: "2.3 million",
      number_description: "individuals listening to radio name daily",
      resources: "Radio, Podcast, Mentorship",
      change: "Renewed faith, fresh hope",
      link: "about:blank"
    },
    {
      id: 2,
      name: "Daniel",
      location: "Brazil",
      image: "/images/stories/daniel-story.jpg",
      avatar: "/images/stories/daniel-story.jpg",
      title: "See how Amina rebuilt his life after loss through our men's mentorship program",
      number: "500+",
      number_description: "individuals connected through our programs this year",
      resources: "Mentorship, Counseling, Online Prayer Community",
      change: "Restored hope, strengthened family",
      link: "about:blank"
    },
    {
      id: 3,
      name: "Marco",
      location: "West Africa",
      image: "/images/stories/marco-story.jpg",
      avatar: "/images/stories/marco-story.jpg",
      title: "See how Marco rebuilt trust and faith through the support of a small group community",
      number: "600+",
      number_description: "people engaged in small groups last year",
      resources: "In-person meetups, mentorship, online small groups, community prayer",
      change: "Restored relationships, deeper faith, lasting support",
      link: "about:blank"
    }
  ];

  // Handle progress bar animation and story transitions
  useEffect(() => {
    // Clear any existing interval
    if (progressRef.current) {
      clearInterval(progressRef.current);
    }

    // Start with full progress bar
    setProgress(100);

    // Wait 1 second before starting progress animation
    const delayTimeout = setTimeout(() => {
      // Reset to 0 and start timing
      setProgress(0);
      startTimeRef.current = Date.now();

      // Start progress animation
      const interval = 50; // Update every 50ms for smoother animation

      progressRef.current = setInterval(() => {
        const elapsed = Date.now() - startTimeRef.current;
        const newProgress = (elapsed / autoSlideInterval) * 100;
        
        if (newProgress >= 100) {
          // Progress complete, move to next story
          setProgress(100);
          const nextStory = (activeStory + 1) % stories.length;
          setActiveStory(nextStory);
        } else {
          setProgress(newProgress);
        }
      }, interval);
    }, 1000); // 1 second delay

    return () => {
      clearTimeout(delayTimeout);
      if (progressRef.current) clearInterval(progressRef.current);
    };
  }, [activeStory, stories.length, autoSlideInterval]);

  const handleStoryChange = (index: number) => {
    if (index !== activeStory) {
      setActiveStory(index);
      console.log("Active story changed to", index);
    }
  };

  return (
    <div className="w-full bg-white">
      <div className="self-stretch px-4 sm:px-8 md:px-16 lg:px-28 py-12 md:py-20 flex flex-col justify-start items-start gap-8 md:gap-16 max-w-[1400px] mx-auto">
        {/* Header Section */}
        <motion.div 
          className="w-full md:w-[750px] rounded-[20px] flex flex-col justify-start items-start gap-6 md:gap-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <div className="self-stretch flex flex-col justify-start items-start gap-3">
            <div className="self-stretch flex flex-col justify-start items-start gap-1">
              <div className="justify-center text-violet-900 text-lg md:text-xl font-semibold font-['Poppins'] leading-7">Stories of Transformation</div>
              <h2 className="self-stretch justify-start text-gray-900 text-3xl md:text-4xl lg:text-5xl font-semibold font-['Poppins'] leading-tight md:leading-[60px]">Jesus is still transforming lives today</h2>
            </div>
            <p className="self-stretch justify-center text-gray-600 text-sm md:text-base font-normal font-['Poppins'] leading-normal">
              Discover how people within our community have encountered hope, healing, and purpose through Jesus. 
              These are real stories of lives changed—through doubt, struggle, and breakthrough. 
              Wherever you are on your journey, you are not alone.
            </p>
          </div>
          <div className="inline-flex justify-start items-center gap-6">
            <PrimaryBtn text="Explore stories" />
          </div>
        </motion.div>

        {/* Story Display Section */}
        <motion.div 
          className="self-stretch flex flex-col justify-start items-start"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="self-stretch flex flex-col justify-start items-start gap-8 md:gap-12">
            {/* Main Story Display */}
            <div className="self-stretch rounded-3xl backdrop-blur-[2px] flex flex-col md:flex-row justify-start min-h-[500px] md:min-h-[488px] items-stretch">
              {/* Stats Column */}
              <div className="w-full md:w-72 py-3 flex flex-col justify-start items-start gap-8 md:gap-16">
                <div className="self-stretch flex flex-col justify-start items-start gap-2">
                  <div className="pl-6 border-l-2 border-violet-900 inline-flex justify-center items-center gap-2.5">
                    <div className="justify-center text-gray-900 text-2xl md:text-4xl font-semibold font-['Poppins'] leading-10">{stories[activeStory].number}</div>
                  </div>
                  <div className="self-stretch pl-6 pr-4 md:pr-12 inline-flex justify-center items-center">
                    <div className="flex-1 justify-center text-gray-700 text-sm md:text-base font-normal font-['Poppins'] leading-normal">
                      {stories[activeStory].number_description}
                    </div>
                  </div>
                </div>
                <div className="self-stretch flex-col justify-start items-start gap-2 hidden md:flex">
                  <div className="pl-6 border-l-2 border-violet-900 inline-flex justify-center items-center gap-2.5">
                    <div className="justify-center text-gray-900 text-lg md:text-xl font-semibold font-['Poppins'] leading-7">Resources accessed</div>
                  </div>
                  <div className="self-stretch pl-6 pr-4 md:pr-12 inline-flex justify-center items-center">
                    <div className="flex-1 self-stretch justify-center text-gray-700 text-sm md:text-base font-normal font-['Poppins'] leading-normal">
                      {stories[activeStory].resources}
                    </div>
                  </div>
                </div>
                <div className="self-stretch flex flex-col justify-start items-start gap-2">
                  <div className="pl-6 border-l-2 border-violet-900 inline-flex justify-center items-center gap-2.5">
                    <div className="justify-center text-gray-900 text-lg md:text-xl font-semibold font-['Poppins'] leading-7">Change experienced</div>
                  </div>
                  <div className="self-stretch pl-6 pr-4 md:pr-12 inline-flex justify-center items-center gap-2.5">
                    <div className="flex-1 self-stretch justify-center text-gray-700 text-sm md:text-base font-normal font-['Poppins'] leading-normal">
                      {stories[activeStory].change}
                    </div>
                  </div>
                </div>
              </div>

              {/* Story Image and Title */}
              <div 
                className="flex-1 min-h-[300px] md:h-[488px] relative rounded-xl overflow-hidden mt-6 md:mt-0"
                onMouseEnter={() => setIsImageHovered(true)}
                onMouseLeave={() => setIsImageHovered(false)}
              >
                <AnimatePresence mode="wait">
                  <motion.div
                    key={activeStory}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.5 }}
                    className="w-full h-full"
                  >
                    <Image 
                      src={stories[activeStory].image} 
                      alt={`${stories[activeStory].name}'s story`}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
                    <div className="w-full h-full p-6 md:p-12 absolute inset-0 flex flex-col justify-end items-start">
                      {/* Fixed position title */}
                      <div className="w-full max-w-[600px] justify-center text-white text-xl md:text-2xl lg:text-3xl font-semibold font-['Poppins'] leading-tight md:leading-9 mb-4">
                        {stories[activeStory].title}
                      </div>
                      
                      {/* Button container with fixed height to prevent layout shift */}
                      <div className="h-2">
                        <AnimatePresence>
                          {isImageHovered && (
                            <motion.div
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: 10 }}
                              transition={{ duration: 0.2 }}
                            >
                              <SecondaryBtn text="Read story" link={stories[activeStory].link} />
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    </div>
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>

            {/* Avatar Selection Row - Desktop */}
            <div 
              className="self-stretch hidden md:grid grid-cols-4 gap-2"
            >
              {stories.map((story, index) => (
                <motion.div
                  key={story.id}
                  className="p-4 md:p-8 flex justify-start items-center gap-4 md:gap-6 cursor-pointer relative"
                  onClick={() => handleStoryChange(index)}
                  whileHover={{ opacity: 0.8 }}
                  animate={{ 
                    opacity: activeStory === index ? 1 : 0.4 
                  }}
                  transition={{ duration: 0.2 }}
                >
                  {/* faded line behind progress bar */}
                  {activeStory === index && (
                    <div className="absolute top-0 left-0 h-0.5 bg-secondary-100 w-full" />
                  )}

                  {activeStory === index && (
                    <motion.div 
                      className="absolute top-0 left-0 h-0.5 bg-secondary-400"
                      style={{ 
                        width: `${progress}%`,
                      }}
                      transition={{ 
                        type: "tween",
                        duration: 0.1,
                        ease: "linear"
                      }}
                    />
                  )}
                  <div className="w-12 h-12 md:w-16 md:h-16 rounded-full overflow-hidden flex-shrink-0">
                    <Image 
                      src={story.avatar} 
                      alt={story.name}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex flex-col justify-center items-start gap-1">
                    <div className="justify-center text-gray-900 text-sm font-medium font-['Poppins'] leading-tight">{story.name}</div>
                    <div className="justify-center text-gray-600 text-xs md:text-sm font-normal font-['Poppins'] leading-tight">{story.location}</div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Simple Bar Indicators - Mobile */}
            <div className="flex md:hidden justify-center items-center gap-2 w-full">
              {stories.map((story, index) => (
                <div
                  key={story.id}
                  className={`h-1.5 rounded-full cursor-pointer transition-all duration-300 ${
                    activeStory === index 
                      ? 'bg-violet-900 w-16' 
                      : 'bg-gray-300 w-16'
                  }`}
                  onClick={() => handleStoryChange(index)}
                />
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}