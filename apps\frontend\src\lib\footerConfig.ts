import { IconDefinition } from "@fortawesome/fontawesome-svg-core";
import {
faWhatsappSquare,
faFacebook,
faSpotify,
faYoutube,
} from "@fortawesome/free-brands-svg-icons";
import {
faPrayingHands,
faCirclePlay,
} from "@fortawesome/free-solid-svg-icons";




export const footerConfig = {
branding: {
  logo: {
    src: "/icons/fish-logo-pink.svg",
    alt: "E-mission to Islam Logo",
    width: 70,
    height: 32,
  },
  name: "E-Mission to Islam",
  tagline: "Making heaven more crowded, one soul at a time.",
},
linksBySection: {
  "Getting Started": [
    { label: "Foundations of Christianity", href: "/foundations" },
    { label: "Blog", href: "/blog", isNew: true },
    { label: "Good News", href: "/good-news" },
    { label: "Faith FAQs", href: "/faqs" },
    { label: "Stories of Transformation", href: "/stories" },
  ],
  "Faith-building": [
    { label: "Resources", href: "/resources" },
    { label: "Radio", href: "/radio" },
    { label: "Bible Study Guides", href: "/bible-study" },
    { label: "Bible Reading Plans", href: "/reading-plans" },
    { label: "Daily Prayer", href: "/prayer" },
    { label: "Know God", href: "/know-god" },
  ],
  Connect: [
    { label: "Prayer Request", href: "/prayer-request" },
    { label: "Chat with Sandra", href: "/chat" },
    { label: "Contact us", href: "/contact" },
    { label: "Discipleship", href: "/discipleship" },
    { label: "What's Next", href: "/whats-next" },
    { label: "FAQs", href: "/faqs" },
  ],
},
newsletterSignupUrl: "/api/newsletter",
newsletter: {
  title: "Subscribe to our newsletter",
  description: "Get the latest community news, sent to your inbox weekly.",
  placeholder: "Enter your email",
  buttonText: "Sign up",
  privacyText: 'By clicking "Sign up" you agree to our',
  tosHref: "/terms",
  privacyHref: "/privacy",
},
socialLinks: [
  {
    icon: faWhatsappSquare,
    label: "WhatsApp",
    href: "https://wa.me/",
  },
  {
    icon: faFacebook,
    label: "Facebook",
    href: "https://facebook.com/",
  },
  {
    icon: faYoutube,
    label: "YouTube",
    href: "https://youtube.com/",
  },
  {
    icon: faSpotify,
    label: "Spotify",
    href: "https://spotify.com/",
  },
] as { icon: IconDefinition; label: string; href: string }[],
journeySection: {
  title: "Let's journey together.",
  subtitle: "Walk in faith in real time, 24/7.",
  actions: [
    {
      icon: faCirclePlay,
      label: "Listen Now",
      href: "/listen",
      alt: "play icon",
    },
    {
      icon: faPrayingHands,
      label: "Meet Jesus",
      href: "/meet-jesus",
      alt: "praying hands",
    },
  ],
},
legalLinks: [
  { label: "Privacy Policy", href: "/privacy" },
  { label: "Terms and Conditions", href: "/terms" },
],
copyright: "Â© 2025 E-mission to Islam. All rights reserved.",
};