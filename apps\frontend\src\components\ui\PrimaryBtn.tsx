import { Button } from '@heroui/button';
import React from 'react';
import { motion } from 'framer-motion';

interface PrimaryBtnProps {
  text: string;
  link?: string;
  outline?: boolean;
  onClick?: () => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

export default function PrimaryBtn(PrimaryBtnProps: PrimaryBtnProps) {
    const buttonClass = PrimaryBtnProps.outline 
    ? "bg-white text-secondary-500 border-2 border-gray-300 rounded-full font-['Poppins'] font-semibold px-5 py-1 h-9 text-sm hover:bg-gray-50 transition-colors duration-300 cursor-pointer" 
    : "bg-secondary-500 text-white rounded-full font-['Poppins'] font-semibold px-5 py-1 h-9 text-sm hover:bg-secondary-400 transition-colors duration-300 cursor-pointer";

  return (
      <motion.div
        whileHover={{ scale: 1.05 }}
      transition={{ duration: 0.2 }}
      className="inline-block origin-center"
    >
        <Button 
        className={buttonClass}
        href={PrimaryBtnProps.link}
        onClick={PrimaryBtnProps.onClick}
        >
          {PrimaryBtnProps.text}
        </Button>

      </motion.div>
  );
}
