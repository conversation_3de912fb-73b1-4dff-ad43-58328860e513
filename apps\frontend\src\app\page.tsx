import Navbar from "../components/Navbar";
import SlideshowBanner from '../components/ui/SlideshowBanner';
import { homeSlides } from '../content/HomePage';
import StartFaithJourney from '../components/StartFaithJourney';
import VerseOfTheDay from "../components/VerseOfTheDay";
import NewsletterSubscription from '../components/NewsletterSubscription';
import Faq from "../components/Faq";
import Footer from "../components/Footer";
import StoriesOfTransformation from "@/components/StoriesOfTransformation";
import { footerConfig } from "@/lib/footerConfig";
//import PrayerRequestCard from "../components/PrayerRequestCard";
//import DiscipleshipCard from "../components/DiscipleshipCard";

export default function Home() {
  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      {/* Slideshow Banner */}
      <SlideshowBanner slides={homeSlides} autoSlideInterval={7000} />
      {/* Start your faith journey */}
      <StartFaithJourney />

      {/* Verse of Day */}
      <VerseOfTheDay /> {/*Now included */}

      {/* Prayer Request */}
      {/* <PrayerRequestCard /> */}

      {/* TODO: ADD COMPONENT - Featured verses */}

      {/* TODO: ADD COMPONENT - Story of the day */}

      {/* Newsletter Subscription */}
      <NewsletterSubscription />

      {/* FAQ Section */}
      <Faq />

      {/* Discipleship Card */}
      {/* <DiscipleshipCard /> */}
      <div className="flex-1"></div>
      <Footer {...footerConfig}/>
    </main>
  );
}
