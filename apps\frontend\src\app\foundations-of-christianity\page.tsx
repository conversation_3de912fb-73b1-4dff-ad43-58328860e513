import Footer from '@/components/Footer';
import Navbar from '@/components/Navbar'
import Foundations from '@/components/FoundationsOfChristianity';
import MeetGod from '@/components/MeetGod';

import { footerConfig } from '@/lib/footerConfig';
import NavigatingLifeChallenges from "@/components/NavigatingLifeChallenges"

const FoundationsOfChristianity = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      {/* TODO: ADD COMPONENT - Foundations of Christianity */}
      <Foundations />
    
      {/* TODO: ADD COMPONENT - Meet God */}
        <MeetGod />

      {/* TODO: ADD COMPONENT - Navigating Life's Challenges */}
      <NavigatingLifeChallenges />

      {/* TODO: ADD COMPONENT - Foundations of Christianity /}

      {/ TODO: ADD COMPONENT - Meet God /}

      {/ TODO: ADD COMPONENT - Navigating Life's Challenges /}

      {/ TODO: ADD COMPONENT - Repentance [Banner Image] /}

      {/ TODO: ADD COMPONENT - More ways to follow God /}

      {/ TODO: ADD COMPONENT - Latest Articles */}

      <div className="flex-1"></div>
          <Footer {...footerConfig} />    </div>
        )
      }

export default FoundationsOfChristianity;