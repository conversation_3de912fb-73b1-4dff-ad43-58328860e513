"use client";

import { motion } from 'framer-motion';
import SecondaryBtn from "@/components/ui/SecondaryBtn";
import { useDictionary } from "@/lib/DictionaryContext";
import Image from "next/image";

const NavigatingLifeChallenges = () => {
    const { navigating } = useDictionary();

    return (
        <div className="flex flex-col max-w-screen-2xl lg:flex-row md:flex-row self-stretch lg:px-28 md:px-10 px-4 py-12 lg:py-20 md:py-16 justify-center items-center gap-16">
            {/* Text section */}
            <motion.div 
                className="w-full lg:w-[600px] md:w-[350px] flex flex-col justify-center items-start gap-8 order-2 lg:order-1 md:order-1"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
            >
                <div className="self-stretch flex flex-col justify-start items-start gap-4">
                    <div 
                    data-heading-size="H2" 
                    data-show-body="true" 
                    data-show-subtitle="true" 
                    className="w-full max-w-[750px] min-w-72 flex flex-col justify-start items-start gap-6">
                        <div className="self-stretch flex flex-col justify-start items-start gap-4">
                            <div className=" self-stretch justify-center text-secondary-500 text-xl font-semibold font-['Poppins'] leading-7">
                                {navigating.subtitle}
                            </div>
                            <div className="self-stretch justify-start text-default-900 text-5xl font-semibold font-['Poppins'] leading-[60px]">
                                {navigating.title}
                            </div>
                        </div>
                        <div className="self-stretch justify-center text-default-600 text-base font-normal font-['Poppins'] leading-normal">
                            {navigating.intro}
                        </div>
                    </div>
                    <div className="self-stretch justify-center text-colors-base-default-600 text-base font-normal font-['Poppins'] leading-normal">
                        {navigating.body}
                    </div>
                </div>
                <div data-state="Default" data-variant="Light - Purple" className="inline-flex justify-start items-start">
                    <div className="rounded-[50px] flex justify-center items-center gap-1.5 overflow-hidden">
                        <div className="justify-start text-colors-base-secondary text-sm font-semibold font-['Poppins'] leading-tight">
                            <SecondaryBtn text={navigating.button} />
                        </div>
                        <div data-size="12" className="w-3 h-3 relative overflow-hidden">
                            <div className="w-1.5 h-2.5 left-[3.50px] top-[0.75px] absolute bg-colors-base-secondary" />
                        </div>
                    </div>
                </div>
            </motion.div>
            
            {/* Image section */}
            <motion.div 
                className="relative w-full lg:w-[552px] md:w-[340px] h-[300px] md:h-[500px] rounded-xl overflow-hidden order-1 lg:order-2"
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
            >
                <Image 
                    src="/images/question-mark.png"
                    alt="Question Mark"
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 552px"
                    priority
                />
            </motion.div>
        </div>
    )
}
export default NavigatingLifeChallenges;
//end